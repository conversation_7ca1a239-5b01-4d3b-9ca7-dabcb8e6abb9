package com.crrc.common.utils;

import android.content.Context;
import android.content.SharedPreferences;


public class PrefsUtil {
    private static final String PREFS_NAME = "settings";

    private static PrefsUtil mInstance;
    private SharedPreferences mPrefs;

    public static PrefsUtil getInstance(Context context) {
        if (mInstance == null) {
            mInstance = new PrefsUtil(context);
        }
        return mInstance;
    }

    private PrefsUtil(Context context) {
        mPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }




    @SuppressWarnings("unused")
    private String getParam(String key, String defValue) {
        return mPrefs.getString(key, defValue);
    }

    @SuppressWarnings("unused")
    private void setParam(String key, String value) {
        mPrefs.edit().putString(key, value).apply();
    }

    @SuppressWarnings("unused")
    private float getParam(String key, float defValue) {
        return mPrefs.getFloat(key, defValue);
    }

    @SuppressWarnings("unused")
    private void setParam(String key, float value) {
        mPrefs.edit().putFloat(key, value).apply();
    }

    @SuppressWarnings("unused")
    private long getParam(String key, long defValue) {
        return mPrefs.getLong(key, defValue);
    }

    @SuppressWarnings("unused")
    private void setParam(String key, long value) {
        mPrefs.edit().putLong(key, value).apply();
    }

    @SuppressWarnings("unused")
    private int getParam(String key, int defValue) {
        return mPrefs.getInt(key, defValue);
    }

    @SuppressWarnings("unused")
    private void setParam(String key, int value) {
        mPrefs.edit().putInt(key, value).apply();
    }

    @SuppressWarnings("unused")
    private boolean getParam(String key, boolean defValue) {
        return mPrefs.getBoolean(key, defValue);
    }

    @SuppressWarnings("unused")
    private void setParam(String key, boolean value) {
        mPrefs.edit().putBoolean(key, value).apply();
    }


    public void setBaseUrl(String baseUrl) {
        setParam("url", baseUrl);
    }

    public String getBaseUrl() {
        return getParam("url", "https://577eb1f0.r18.cpolar.top");
    }

}
