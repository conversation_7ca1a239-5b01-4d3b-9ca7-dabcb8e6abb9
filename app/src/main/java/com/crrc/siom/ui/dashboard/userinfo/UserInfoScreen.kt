package com.crrc.siom.ui.dashboard.userinfo

import android.content.ContentResolver
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import android.webkit.MimeTypeMap
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.crrc.siom.data.SessionManager
import com.crrc.siom.ui.login.LoginActivity
import com.crrc.siom.ui.profile.ProfileViewModel
import com.crrc.siom.ui.profile.StatisticsDetailActivity
import java.io.File

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserInfoScreen(
    userId: String,
) {
    val viewModel: UserInfoViewModel = viewModel(
        factory = UserInfoViewModelFactory(userId)
    )
    val isLoading by viewModel.isLoading.observeAsState(false)
    val userInfo by viewModel.userInfo.observeAsState()
    val errorMessage by viewModel.errorMessage.observeAsState()
    val context = LocalContext.current

    LaunchedEffect(userId) {
        viewModel.loadUserInfo(userId)
    }


    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
        return
    }
    
    if (errorMessage != null && userInfo == null) {
        // 显示错误
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = errorMessage!!,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(onClick = { viewModel.loadUserInfo(userId) }) {
                    Text("重试")
                }
            }
        }
        return
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 头像和名称区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.primary)
                .padding(bottom = 16.dp, top = 16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 头像 - 点击可以上传新头像
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surface),
                    contentAlignment = Alignment.Center
                ) {
                    if (userInfo?.avatar.isNullOrEmpty()) {
                        // 如果没有头像URL则显示默认图标
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "头像",
                            modifier = Modifier
                                .size(40.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    } else {
                        // 从URL加载头像
                        AsyncImage(
                            model = ImageRequest.Builder(context)
                                .data(userInfo?.avatar)
                                .crossfade(true)
                                .build(),
                            contentDescription = "头像",
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                // 名称和部门
                Column {
                    Text(
                        text = userInfo?.name ?: "未知用户",
                        style = MaterialTheme.typography.headlineSmall,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = userInfo?.department ?: "未知部门",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "个人信息",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                userInfo?.let { user ->
                    InfoItem("工号", user.number ?: "未设置")
                    InfoItem("部门", user.department ?: "未设置")
                    InfoItem("班组", user.group ?: "未设置")
                    InfoItem("性别", user.sex ?: "未设置")
                    InfoItem("年龄", user.age ?: "未设置")
                    InfoItem("手机号", user.phone ?: "未设置")
                } ?: run {
                    // 用户信息为空时的默认显示
                    InfoItem("工号", "未知")
                    InfoItem("部门", "未知")
                    InfoItem("班组", "未知")
                    InfoItem("性别", "未知")
                    InfoItem("年龄", "未知")
                    InfoItem("手机号", "未知")
                }
            }
        }
    }
}

// 获取URI的MIME类型
private fun getMimeType(contentResolver: ContentResolver, uri: Uri): String? {
    return if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
        contentResolver.getType(uri)?.let {
            MimeTypeMap.getSingleton().getExtensionFromMimeType(it)
        }
    } else {
        MimeTypeMap.getFileExtensionFromUrl(uri.toString())
    }
}

@Composable
private fun InfoItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
} 