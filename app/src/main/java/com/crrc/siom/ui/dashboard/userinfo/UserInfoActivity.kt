package com.crrc.siom.ui.dashboard.userinfo

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import com.crrc.siom.ui.profile.ProfileScreen
import com.crrc.siom.ui.theme.SiomTheme

class UserInfoActivity : ComponentActivity() {

    companion object {
        private const val USER_ID = "user_id"

        fun start(context: Context,userId: String) {
             val intent = Intent(context, UserInfoActivity::class.java).apply {
                 putExtra(USER_ID, userId)
             }
            context.startActivity(intent)
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val userId = intent.getStringExtra(USER_ID)
        setContent {
            SiomTheme {
                Scaffold(
                    topBar = {
                        TopAppBar(
                            title = { Text("设备详情") },
                            navigationIcon = {
                                IconButton(onClick = { (context as? Activity)?.finish() }) {
                                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                                }
                            }
                        )
                    }
                ){
                    UserInfoScreen(userId.toString())
                }
            }
        }
    }
} 